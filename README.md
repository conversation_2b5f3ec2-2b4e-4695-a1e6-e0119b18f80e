# عارض جهات الاتصال - Contact Viewer

تطبيق ويب حديث ومتطور لعرض وإدارة جهات الاتصال من ملفات VCF (vCard) بتصميم زجاجي أنيق وخصائص متقدمة.

## ✨ الخصائص الرئيسية

### 📁 رفع الملفات
- دعم رفع ملفات VCF و vCard متعددة
- السحب والإفلات المباشر
- معالجة الملفات الكبيرة بكفاءة

### 🎨 التصميم
- تصميم زجاجي حديث (Glassmorphism)
- واجهة مستخدم متجاوبة تدعم جميع الأجهزة
- خطوط عربية جميلة (Cairo Font)
- رسوم متحركة سلسة وجذابة

### 🔍 البحث والتصفية
- البحث السريع في جميع بيانات جهات الاتصال
- تصفية حسب نوع البيانات (هاتف، بريد إلكتروني، عنوان)
- نتائج فورية أثناء الكتابة

### 📊 الإحصائيات
- عرض إحصائيات مفصلة عن جهات الاتصال
- رسوم متحركة للأرقام
- تحديث فوري للبيانات

### 📱 عرض البيانات
- كروت جهات اتصال أنيقة ومنظمة
- عرض تفصيلي في نوافذ منبثقة
- أفاتار تلقائي للأسماء

### 🛠️ الأدوات المتقدمة
- نسخ بيانات جهات الاتصال
- مشاركة جهات الاتصال
- تصدير البيانات إلى CSV
- إشعارات تفاعلية

## 🚀 كيفية الاستخدام

### 1. فتح التطبيق
افتح ملف `index.html` في متصفح الويب الحديث.

### 2. رفع ملفات VCF
- اسحب وأفلت ملفات VCF على المنطقة المخصصة
- أو انقر لاختيار الملفات من جهازك
- يمكن رفع ملفات متعددة في نفس الوقت

### 3. استكشاف البيانات
- استخدم مربع البحث للعثور على جهات اتصال محددة
- استخدم قائمة التصفية لعرض فئات معينة
- انقر على أي كرت لعرض التفاصيل الكاملة

### 4. إدارة البيانات
- انقر على "تصدير" لحفظ البيانات كملف CSV
- استخدم "نسخ البيانات" لنسخ معلومات جهة اتصال
- استخدم "مشاركة" لمشاركة بيانات جهة الاتصال

## 🔧 المتطلبات التقنية

### متصفحات مدعومة
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### الخصائص المطلوبة
- دعم JavaScript ES6+
- دعم CSS Grid و Flexbox
- دعم File API
- دعم Clipboard API (للنسخ)
- دعم Web Share API (للمشاركة - اختياري)

## 📋 تنسيقات VCF المدعومة

التطبيق يدعم معظم خصائص vCard القياسية:

### البيانات الأساسية
- `FN` - الاسم الكامل
- `N` - الاسم المنظم (الأول، الأخير، الأوسط، اللقب)
- `ORG` - المؤسسة
- `TITLE` - المنصب

### معلومات الاتصال
- `TEL` - أرقام الهاتف (جوال، منزل، عمل، فاكس)
- `EMAIL` - عناوين البريد الإلكتروني
- `ADR` - العناوين البريدية
- `URL` - المواقع الإلكترونية

### معلومات إضافية
- `NOTE` - الملاحظات
- `BDAY` - تاريخ الميلاد
- `PHOTO` - الصورة الشخصية
- `CATEGORIES` - التصنيفات

## 🎯 الخصائص المتقدمة

### تحليل ذكي للبيانات
- استخراج أنواع أرقام الهاتف تلقائياً
- تنظيف وتنسيق البيانات
- دعم التشفير والأحرف الخاصة

### واجهة مستخدم ذكية
- تحديث فوري للنتائج
- حالات فارغة واضحة
- رسائل خطأ مفيدة
- شاشات تحميل أنيقة

### الأداء
- معالجة سريعة للملفات الكبيرة
- ذاكرة محسنة للبيانات
- رسوم متحركة محسنة للأداء

## 🔒 الأمان والخصوصية

- جميع البيانات تتم معالجتها محلياً في المتصفح
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- لا يتم حفظ البيانات بشكل دائم
- إمكانية مسح البيانات بالكامل في أي وقت

## 🛠️ التطوير والتخصيص

### بنية الملفات
```
Contact_Viewer/
├── index.html          # الصفحة الرئيسية
├── style.css           # أنماط التصميم الزجاجي
├── script.js           # منطق التطبيق الرئيسي
├── vcf-parser.js       # مكتبة تحليل ملفات VCF
└── README.md           # دليل الاستخدام
```

### التخصيص
يمكن تخصيص التطبيق بسهولة من خلال:
- تعديل الألوان في ملف `style.css`
- إضافة خصائص جديدة في `vcf-parser.js`
- تخصيص واجهة المستخدم في `script.js`

## 📞 الدعم والمساعدة

### مشاكل شائعة
1. **الملف لا يتم تحميله**: تأكد من أن الملف بصيغة .vcf أو .vcard
2. **البيانات لا تظهر**: تحقق من صحة تنسيق ملف VCF
3. **البحث لا يعمل**: تأكد من وجود بيانات محملة

### نصائح للاستخدام الأمثل
- استخدم ملفات VCF محدثة ومتوافقة مع المعايير
- تأكد من تشغيل JavaScript في المتصفح
- استخدم متصفح حديث للحصول على أفضل تجربة

## 🎉 الخلاصة

عارض جهات الاتصال هو تطبيق ويب متطور يوفر تجربة حديثة وسهلة لإدارة جهات الاتصال من ملفات VCF. بتصميمه الزجاجي الأنيق وخصائصه المتقدمة، يوفر حلاً شاملاً لعرض وتنظيم بيانات جهات الاتصال بطريقة احترافية وجذابة.

---

**تم التطوير بـ ❤️ باستخدام تقنيات الويب الحديثة**
