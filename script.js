/**
 * Contact Viewer - التطبيق الرئيسي
 * تطبيق ويب لعرض وإدارة جهات الاتصال من ملفات VCF
 */

class ContactViewer {
    constructor() {
        this.parser = new VCFParser();
        this.allContacts = [];
        this.filteredContacts = [];
        this.currentFilter = 'all';
        this.currentSearch = '';
        
        this.initializeElements();
        this.bindEvents();
    }

    /**
     * تهيئة عناصر DOM
     */
    initializeElements() {
        // عناصر رفع الملفات
        this.uploadSection = document.getElementById('uploadSection');
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        
        // عناصر التحكم
        this.controlsSection = document.getElementById('controlsSection');
        this.searchInput = document.getElementById('searchInput');
        this.filterSelect = document.getElementById('filterSelect');
        this.exportBtn = document.getElementById('exportBtn');
        this.clearBtn = document.getElementById('clearBtn');
        
        // عناصر الإحصائيات
        this.statsSection = document.getElementById('statsSection');
        this.totalContacts = document.getElementById('totalContacts');
        this.withPhone = document.getElementById('withPhone');
        this.withEmail = document.getElementById('withEmail');
        this.withAddress = document.getElementById('withAddress');
        
        // عناصر عرض جهات الاتصال
        this.contactsSection = document.getElementById('contactsSection');
        this.contactsGrid = document.getElementById('contactsGrid');
        
        // عناصر أخرى
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.emptyState = document.getElementById('emptyState');
        
        // عناصر النافذة المنبثقة
        this.modalOverlay = document.getElementById('modalOverlay');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalBody = document.getElementById('modalBody');
        this.modalClose = document.getElementById('modalClose');
        this.copyContactBtn = document.getElementById('copyContactBtn');
        this.shareContactBtn = document.getElementById('shareContactBtn');
        
        // حاوية الإشعارات
        this.toastContainer = document.getElementById('toastContainer');
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // أحداث رفع الملفات
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // أحداث السحب والإفلات
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // أحداث البحث والتصفية
        this.searchInput.addEventListener('input', (e) => this.handleSearch(e));
        this.filterSelect.addEventListener('change', (e) => this.handleFilter(e));
        
        // أحداث الأزرار
        this.exportBtn.addEventListener('click', () => this.exportContacts());
        this.clearBtn.addEventListener('click', () => this.clearContacts());
        
        // أحداث النافذة المنبثقة
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modalOverlay.addEventListener('click', (e) => {
            if (e.target === this.modalOverlay) this.closeModal();
        });
        this.copyContactBtn.addEventListener('click', () => this.copyCurrentContact());
        this.shareContactBtn.addEventListener('click', () => this.shareCurrentContact());
        
        // إغلاق النافذة المنبثقة بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modalOverlay.style.display !== 'none') {
                this.closeModal();
            }
        });
    }

    /**
     * معالجة اختيار الملفات
     */
    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        this.processFiles(files);
    }

    /**
     * معالجة السحب فوق المنطقة
     */
    handleDragOver(event) {
        event.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    /**
     * معالجة مغادرة منطقة السحب
     */
    handleDragLeave(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    /**
     * معالجة إفلات الملفات
     */
    handleDrop(event) {
        event.preventDefault();
        this.uploadArea.classList.remove('dragover');
        
        const files = Array.from(event.dataTransfer.files);
        const vcfFiles = files.filter(file => 
            file.name.toLowerCase().endsWith('.vcf') || 
            file.name.toLowerCase().endsWith('.vcard')
        );
        
        if (vcfFiles.length === 0) {
            this.showToast('يرجى اختيار ملفات VCF صحيحة', 'error');
            return;
        }
        
        this.processFiles(vcfFiles);
    }

    /**
     * معالجة الملفات المرفوعة
     */
    async processFiles(files) {
        this.showLoading(true);
        
        try {
            let allContacts = [];
            
            for (const file of files) {
                const content = await this.readFileContent(file);
                const contacts = this.parser.parse(content);
                allContacts = allContacts.concat(contacts);
            }
            
            if (allContacts.length === 0) {
                this.showToast('لم يتم العثور على جهات اتصال صحيحة في الملفات', 'error');
                this.showLoading(false);
                return;
            }
            
            this.allContacts = allContacts;
            this.filteredContacts = [...this.allContacts];
            
            this.showContacts();
            this.updateStats();
            this.showSections();
            
            this.showToast(`تم تحميل ${allContacts.length} جهة اتصال بنجاح`, 'success');
            
        } catch (error) {
            console.error('خطأ في معالجة الملفات:', error);
            this.showToast('حدث خطأ أثناء معالجة الملفات', 'error');
        }
        
        this.showLoading(false);
    }

    /**
     * قراءة محتوى الملف
     */
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file, 'UTF-8');
        });
    }

    /**
     * عرض/إخفاء شاشة التحميل
     */
    showLoading(show) {
        this.loadingSpinner.style.display = show ? 'block' : 'none';
    }

    /**
     * إظهار الأقسام بعد تحميل البيانات
     */
    showSections() {
        this.controlsSection.style.display = 'block';
        this.statsSection.style.display = 'block';
        this.contactsSection.style.display = 'block';
        this.uploadSection.style.display = 'none';
    }

    /**
     * تحديث الإحصائيات
     */
    updateStats() {
        const total = this.allContacts.length;
        const withPhone = this.allContacts.filter(c => c.phones.length > 0).length;
        const withEmail = this.allContacts.filter(c => c.emails.length > 0).length;
        const withAddress = this.allContacts.filter(c => c.addresses.length > 0).length;
        
        this.animateNumber(this.totalContacts, total);
        this.animateNumber(this.withPhone, withPhone);
        this.animateNumber(this.withEmail, withEmail);
        this.animateNumber(this.withAddress, withAddress);
    }

    /**
     * تحريك الأرقام في الإحصائيات
     */
    animateNumber(element, targetNumber) {
        const duration = 1000;
        const startNumber = 0;
        const startTime = Date.now();
        
        const updateNumber = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
            
            element.textContent = currentNumber;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        };
        
        updateNumber();
    }

    /**
     * معالجة البحث
     */
    handleSearch(event) {
        this.currentSearch = event.target.value;
        this.applyFilters();
    }

    /**
     * معالجة التصفية
     */
    handleFilter(event) {
        this.currentFilter = event.target.value;
        this.applyFilters();
    }

    /**
     * تطبيق الفلاتر والبحث
     */
    applyFilters() {
        let contacts = [...this.allContacts];
        
        // تطبيق التصفية
        if (this.currentFilter !== 'all') {
            contacts = this.parser.filterContacts(this.currentFilter);
        }
        
        // تطبيق البحث
        if (this.currentSearch.trim()) {
            const searchTerm = this.currentSearch.toLowerCase().trim();
            contacts = contacts.filter(contact => {
                return contact.name.full.toLowerCase().includes(searchTerm) ||
                       contact.organization.toLowerCase().includes(searchTerm) ||
                       contact.phones.some(phone => phone.number.includes(searchTerm)) ||
                       contact.emails.some(email => email.address.toLowerCase().includes(searchTerm));
            });
        }
        
        this.filteredContacts = contacts;
        this.showContacts();
    }

    /**
     * عرض جهات الاتصال
     */
    showContacts() {
        if (this.filteredContacts.length === 0) {
            this.contactsGrid.innerHTML = '';
            this.emptyState.style.display = 'block';
            return;
        }
        
        this.emptyState.style.display = 'none';
        
        const contactsHTML = this.filteredContacts.map(contact => 
            this.createContactCard(contact)
        ).join('');
        
        this.contactsGrid.innerHTML = contactsHTML;
        
        // ربط أحداث النقر على الكروت
        this.contactsGrid.querySelectorAll('.contact-card').forEach(card => {
            card.addEventListener('click', () => {
                const contactId = card.dataset.contactId;
                const contact = this.allContacts.find(c => c.id === contactId);
                if (contact) {
                    this.showContactModal(contact);
                }
            });
        });
    }

    /**
     * إنشاء كرت جهة اتصال
     */
    createContactCard(contact) {
        const avatar = this.getContactAvatar(contact);
        const primaryPhone = contact.phones.length > 0 ? contact.phones[0].number : '';
        const primaryEmail = contact.emails.length > 0 ? contact.emails[0].address : '';
        const primaryAddress = contact.addresses.length > 0 ? contact.addresses[0].formatted : '';
        
        return `
            <div class="contact-card" data-contact-id="${contact.id}">
                <div class="contact-header">
                    <div class="contact-avatar">${avatar}</div>
                    <div class="contact-info">
                        <h3>${this.escapeHtml(contact.name.full || 'بدون اسم')}</h3>
                        ${contact.organization ? `<div class="contact-org">${this.escapeHtml(contact.organization)}</div>` : ''}
                    </div>
                </div>
                <div class="contact-details">
                    ${primaryPhone ? `
                        <div class="contact-detail">
                            <i class="fas fa-phone"></i>
                            <span>${this.escapeHtml(primaryPhone)}</span>
                        </div>
                    ` : ''}
                    ${primaryEmail ? `
                        <div class="contact-detail">
                            <i class="fas fa-envelope"></i>
                            <span>${this.escapeHtml(primaryEmail)}</span>
                        </div>
                    ` : ''}
                    ${primaryAddress ? `
                        <div class="contact-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${this.escapeHtml(primaryAddress)}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * الحصول على الأحرف الأولى للاسم للأفاتار
     */
    getContactAvatar(contact) {
        if (!contact.name.full) return '👤';
        
        const words = contact.name.full.trim().split(' ');
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        } else {
            return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
        }
    }

    /**
     * تنظيف النص من HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * عرض نافذة تفاصيل جهة الاتصال
     */
    showContactModal(contact) {
        this.currentContact = contact;
        this.modalTitle.textContent = contact.name.full || 'بدون اسم';
        
        const detailsHTML = this.createContactDetails(contact);
        this.modalBody.innerHTML = detailsHTML;
        
        this.modalOverlay.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    /**
     * إغلاق النافذة المنبثقة
     */
    closeModal() {
        this.modalOverlay.style.display = 'none';
        document.body.style.overflow = 'auto';
        this.currentContact = null;
    }

    /**
     * إنشاء تفاصيل جهة الاتصال للنافذة المنبثقة
     */
    createContactDetails(contact) {
        let html = '';
        
        // الاسم والمؤسسة
        if (contact.name.full) {
            html += this.createDetailItem('fas fa-user', 'الاسم', contact.name.full);
        }
        
        if (contact.organization) {
            html += this.createDetailItem('fas fa-building', 'المؤسسة', contact.organization);
        }
        
        if (contact.title) {
            html += this.createDetailItem('fas fa-briefcase', 'المنصب', contact.title);
        }
        
        // أرقام الهاتف
        contact.phones.forEach(phone => {
            const typeLabel = this.getPhoneTypeLabel(phone.type);
            html += this.createDetailItem('fas fa-phone', `هاتف (${typeLabel})`, phone.number);
        });
        
        // البريد الإلكتروني
        contact.emails.forEach(email => {
            const typeLabel = this.getEmailTypeLabel(email.type);
            html += this.createDetailItem('fas fa-envelope', `بريد إلكتروني (${typeLabel})`, email.address);
        });
        
        // العناوين
        contact.addresses.forEach(address => {
            const typeLabel = this.getAddressTypeLabel(address.type);
            html += this.createDetailItem('fas fa-map-marker-alt', `عنوان (${typeLabel})`, address.formatted);
        });
        
        // المواقع الإلكترونية
        contact.urls.forEach(url => {
            html += this.createDetailItem('fas fa-globe', 'موقع إلكتروني', url.url);
        });
        
        // الملاحظات
        if (contact.notes) {
            html += this.createDetailItem('fas fa-sticky-note', 'ملاحظات', contact.notes);
        }
        
        // تاريخ الميلاد
        if (contact.birthday) {
            html += this.createDetailItem('fas fa-birthday-cake', 'تاريخ الميلاد', contact.birthday);
        }
        
        return html;
    }

    /**
     * إنشاء عنصر تفصيل واحد
     */
    createDetailItem(icon, label, value) {
        return `
            <div class="modal-contact-detail">
                <i class="${icon}"></i>
                <div class="detail-content">
                    <div class="detail-label">${label}</div>
                    <div class="detail-value">${this.escapeHtml(value)}</div>
                </div>
            </div>
        `;
    }

    /**
     * الحصول على تسمية نوع الهاتف
     */
    getPhoneTypeLabel(type) {
        const labels = {
            'mobile': 'جوال',
            'home': 'منزل',
            'work': 'عمل',
            'fax': 'فاكس',
            'other': 'أخرى'
        };
        return labels[type] || 'أخرى';
    }

    /**
     * الحصول على تسمية نوع البريد الإلكتروني
     */
    getEmailTypeLabel(type) {
        const labels = {
            'home': 'شخصي',
            'work': 'عمل',
            'other': 'أخرى'
        };
        return labels[type] || 'أخرى';
    }

    /**
     * الحصول على تسمية نوع العنوان
     */
    getAddressTypeLabel(type) {
        const labels = {
            'home': 'منزل',
            'work': 'عمل',
            'other': 'أخرى'
        };
        return labels[type] || 'أخرى';
    }

    /**
     * نسخ بيانات جهة الاتصال الحالية
     */
    async copyCurrentContact() {
        if (!this.currentContact) return;
        
        const contactText = this.formatContactForCopy(this.currentContact);
        
        try {
            await navigator.clipboard.writeText(contactText);
            this.showToast('تم نسخ بيانات جهة الاتصال', 'success');
        } catch (error) {
            console.error('خطأ في النسخ:', error);
            this.showToast('فشل في نسخ البيانات', 'error');
        }
    }

    /**
     * تنسيق جهة الاتصال للنسخ
     */
    formatContactForCopy(contact) {
        let text = '';
        
        if (contact.name.full) text += `الاسم: ${contact.name.full}\n`;
        if (contact.organization) text += `المؤسسة: ${contact.organization}\n`;
        if (contact.title) text += `المنصب: ${contact.title}\n`;
        
        contact.phones.forEach(phone => {
            text += `هاتف: ${phone.number}\n`;
        });
        
        contact.emails.forEach(email => {
            text += `بريد إلكتروني: ${email.address}\n`;
        });
        
        contact.addresses.forEach(address => {
            text += `عنوان: ${address.formatted}\n`;
        });
        
        return text.trim();
    }

    /**
     * مشاركة جهة الاتصال الحالية
     */
    async shareCurrentContact() {
        if (!this.currentContact) return;
        
        const contactText = this.formatContactForCopy(this.currentContact);
        
        if (navigator.share) {
            try {
                await navigator.share({
                    title: `جهة اتصال: ${this.currentContact.name.full}`,
                    text: contactText
                });
            } catch (error) {
                console.error('خطأ في المشاركة:', error);
            }
        } else {
            // نسخ إلى الحافظة كبديل
            await this.copyCurrentContact();
        }
    }

    /**
     * تصدير جهات الاتصال
     */
    exportContacts() {
        if (this.filteredContacts.length === 0) {
            this.showToast('لا توجد جهات اتصال للتصدير', 'error');
            return;
        }
        
        const csvContent = this.generateCSV(this.filteredContacts);
        this.downloadFile(csvContent, 'contacts.csv', 'text/csv');
        
        this.showToast(`تم تصدير ${this.filteredContacts.length} جهة اتصال`, 'success');
    }

    /**
     * توليد ملف CSV
     */
    generateCSV(contacts) {
        const headers = ['الاسم', 'المؤسسة', 'المنصب', 'الهاتف', 'البريد الإلكتروني', 'العنوان'];
        let csv = headers.join(',') + '\n';
        
        contacts.forEach(contact => {
            const row = [
                this.escapeCsvValue(contact.name.full),
                this.escapeCsvValue(contact.organization),
                this.escapeCsvValue(contact.title),
                this.escapeCsvValue(contact.phones.map(p => p.number).join('; ')),
                this.escapeCsvValue(contact.emails.map(e => e.address).join('; ')),
                this.escapeCsvValue(contact.addresses.map(a => a.formatted).join('; '))
            ];
            csv += row.join(',') + '\n';
        });
        
        return csv;
    }

    /**
     * تنظيف قيم CSV
     */
    escapeCsvValue(value) {
        if (!value) return '""';
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return '"' + stringValue.replace(/"/g, '""') + '"';
        }
        return '"' + stringValue + '"';
    }

    /**
     * تحميل ملف
     */
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }

    /**
     * مسح جميع جهات الاتصال
     */
    clearContacts() {
        if (confirm('هل أنت متأكد من مسح جميع جهات الاتصال؟')) {
            this.allContacts = [];
            this.filteredContacts = [];
            
            this.controlsSection.style.display = 'none';
            this.statsSection.style.display = 'none';
            this.contactsSection.style.display = 'none';
            this.uploadSection.style.display = 'block';
            
            this.searchInput.value = '';
            this.filterSelect.value = 'all';
            this.fileInput.value = '';
            
            this.showToast('تم مسح جميع جهات الاتصال', 'info');
        }
    }

    /**
     * عرض إشعار
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icon = type === 'success' ? 'fas fa-check-circle' : 
                    type === 'error' ? 'fas fa-exclamation-circle' : 
                    'fas fa-info-circle';
        
        toast.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;
        
        this.toastContainer.appendChild(toast);
        
        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new ContactViewer();
});
