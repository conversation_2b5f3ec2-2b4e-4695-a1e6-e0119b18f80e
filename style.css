/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
.background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -150px;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -100px;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Glass Card Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Header */
.header {
    text-align: center;
    padding: 30px;
    margin-bottom: 30px;
}

.header h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    font-weight: 300;
}

.header i {
    margin-left: 15px;
    color: #ffd700;
}

/* Upload Section */
.upload-section {
    margin-bottom: 30px;
    padding: 40px;
    text-align: center;
}

.upload-area {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 60px 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.upload-area:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.05);
}

.upload-area.dragover {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
}

.upload-area h3 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.upload-area p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    margin-bottom: 20px;
}

#fileInput {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-info {
    margin-top: 15px;
}

.upload-info small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

/* Controls Section */
.controls-section {
    margin-bottom: 30px;
}

.controls {
    padding: 25px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-container {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-container i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
}

#searchInput {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

#searchInput::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

#searchInput:focus {
    border-color: #ffd700;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.filter-container select {
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    outline: none;
    cursor: pointer;
    min-width: 180px;
}

.filter-container select option {
    background: #333;
    color: white;
}

.actions-container {
    display: flex;
    gap: 10px;
}

.btn-action {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.btn-action:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-danger:hover {
    background: rgba(255, 0, 0, 0.3);
}

/* Stats Section */
.stats-section {
    margin-bottom: 30px;
}

.stats {
    padding: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 5px;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    font-weight: 400;
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: 60px;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px;
    color: rgba(255, 255, 255, 0.7);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: white;
}

/* Contacts Grid */
.contacts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.contact-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffd700, #ff6b6b, #4ecdc4);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.contact-card:hover::before {
    opacity: 1;
}

.contact-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.contact-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffd700, #ff6b6b);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    margin-left: 15px;
    flex-shrink: 0;
}

.contact-info h3 {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    line-height: 1.3;
}

.contact-info .contact-org {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.contact-details {
    space-y: 8px;
}

.contact-detail {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.contact-detail i {
    width: 20px;
    margin-left: 10px;
    color: #ffd700;
    flex-shrink: 0;
}

.contact-detail span {
    flex: 1;
    word-break: break-all;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.modal {
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 25px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: white;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.modal-body {
    padding: 25px;
}

.modal-contact-detail {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.modal-contact-detail:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-contact-detail i {
    width: 25px;
    color: #ffd700;
    margin-left: 15px;
    margin-top: 2px;
    flex-shrink: 0;
}

.modal-contact-detail .detail-content {
    flex: 1;
}

.modal-contact-detail .detail-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modal-contact-detail .detail-value {
    color: white;
    font-size: 1rem;
    word-break: break-all;
    line-height: 1.4;
}

.modal-footer {
    padding: 15px 25px 25px;
    display: flex;
    gap: 10px;
    justify-content: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 2000;
    pointer-events: none;
}

.toast {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 10px;
    color: white;
    font-size: 0.9rem;
    animation: toastSlideIn 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 250px;
}

.toast.success {
    border-left: 4px solid #4ecdc4;
}

.toast.error {
    border-left: 4px solid #ff6b6b;
}

.toast.info {
    border-left: 4px solid #ffd700;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-container {
        min-width: auto;
    }

    .actions-container {
        justify-content: center;
    }

    .stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .contacts-grid {
        grid-template-columns: 1fr;
    }

    .modal {
        margin: 10px;
        max-height: 90vh;
    }

    .toast-container {
        left: 10px;
        right: 10px;
    }

    .toast {
        min-width: auto;
    }
}
