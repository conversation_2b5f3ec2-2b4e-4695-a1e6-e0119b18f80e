/**
 * VCF Parser - مكتبة تحليل ملفات vCard
 * يدعم تحليل ملفات VCF واستخلاص بيانات جهات الاتصال
 */

class VCFParser {
    constructor() {
        this.contacts = [];
    }

    /**
     * تحليل محتوى ملف VCF
     * @param {string} vcfContent - محتوى ملف VCF
     * @returns {Array} مصفوفة جهات الاتصال
     */
    parse(vcfContent) {
        this.contacts = [];
        
        // تنظيف المحتوى وتقسيمه إلى أسطر
        const lines = vcfContent
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        let currentContact = null;
        let isInContact = false;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            // بداية جهة اتصال جديدة
            if (line.startsWith('BEGIN:VCARD')) {
                currentContact = this.createEmptyContact();
                isInContact = true;
                continue;
            }
            
            // نهاية جهة الاتصال الحالية
            if (line.startsWith('END:VCARD')) {
                if (currentContact && this.isValidContact(currentContact)) {
                    this.contacts.push(currentContact);
                }
                currentContact = null;
                isInContact = false;
                continue;
            }
            
            // تحليل خصائص جهة الاتصال
            if (isInContact && currentContact) {
                this.parseContactProperty(line, currentContact);
            }
        }

        return this.contacts;
    }

    /**
     * إنشاء كائن جهة اتصال فارغ
     * @returns {Object} كائن جهة اتصال فارغ
     */
    createEmptyContact() {
        return {
            id: this.generateId(),
            name: {
                full: '',
                first: '',
                last: '',
                middle: '',
                prefix: '',
                suffix: ''
            },
            organization: '',
            title: '',
            phones: [],
            emails: [],
            addresses: [],
            urls: [],
            notes: '',
            birthday: '',
            photo: '',
            categories: []
        };
    }

    /**
     * تحليل خاصية من خصائص جهة الاتصال
     * @param {string} line - السطر المراد تحليله
     * @param {Object} contact - كائن جهة الاتصال
     */
    parseContactProperty(line, contact) {
        // التعامل مع الأسطر المتعددة (المطوية)
        if (line.startsWith(' ') || line.startsWith('\t')) {
            return; // تجاهل الأسطر المطوية للبساطة
        }

        const colonIndex = line.indexOf(':');
        if (colonIndex === -1) return;

        const propertyPart = line.substring(0, colonIndex);
        const value = line.substring(colonIndex + 1);

        // تحليل اسم الخاصية والمعاملات
        const [propertyName, ...params] = propertyPart.split(';');
        const parameters = this.parseParameters(params);

        switch (propertyName.toUpperCase()) {
            case 'FN':
                contact.name.full = this.decodeValue(value);
                break;
                
            case 'N':
                this.parseStructuredName(value, contact);
                break;
                
            case 'ORG':
                contact.organization = this.decodeValue(value);
                break;
                
            case 'TITLE':
                contact.title = this.decodeValue(value);
                break;
                
            case 'TEL':
                this.parsePhone(value, parameters, contact);
                break;
                
            case 'EMAIL':
                this.parseEmail(value, parameters, contact);
                break;
                
            case 'ADR':
                this.parseAddress(value, parameters, contact);
                break;
                
            case 'URL':
                contact.urls.push({
                    url: this.decodeValue(value),
                    type: parameters.TYPE || 'other'
                });
                break;
                
            case 'NOTE':
                contact.notes = this.decodeValue(value);
                break;
                
            case 'BDAY':
                contact.birthday = this.decodeValue(value);
                break;
                
            case 'PHOTO':
                contact.photo = this.decodeValue(value);
                break;
                
            case 'CATEGORIES':
                contact.categories = this.decodeValue(value).split(',').map(cat => cat.trim());
                break;
        }
    }

    /**
     * تحليل الاسم المنظم
     * @param {string} value - قيمة الاسم المنظم
     * @param {Object} contact - كائن جهة الاتصال
     */
    parseStructuredName(value, contact) {
        const parts = value.split(';').map(part => this.decodeValue(part));
        contact.name.last = parts[0] || '';
        contact.name.first = parts[1] || '';
        contact.name.middle = parts[2] || '';
        contact.name.prefix = parts[3] || '';
        contact.name.suffix = parts[4] || '';
        
        // إنشاء الاسم الكامل إذا لم يكن موجوداً
        if (!contact.name.full) {
            const nameParts = [
                contact.name.prefix,
                contact.name.first,
                contact.name.middle,
                contact.name.last,
                contact.name.suffix
            ].filter(part => part.length > 0);
            contact.name.full = nameParts.join(' ');
        }
    }

    /**
     * تحليل رقم الهاتف
     * @param {string} value - رقم الهاتف
     * @param {Object} parameters - معاملات الخاصية
     * @param {Object} contact - كائن جهة الاتصال
     */
    parsePhone(value, parameters, contact) {
        const rawNumber = this.decodeValue(value);
        const phone = {
            number: rawNumber,
            formatted: this.formatPhoneNumber(rawNumber),
            type: this.getPhoneType(parameters),
            isPrimary: parameters.PREF === 'true' || parameters.PREF === '1'
        };
        contact.phones.push(phone);
    }

    /**
     * تحليل البريد الإلكتروني
     * @param {string} value - البريد الإلكتروني
     * @param {Object} parameters - معاملات الخاصية
     * @param {Object} contact - كائن جهة الاتصال
     */
    parseEmail(value, parameters, contact) {
        const email = {
            address: this.decodeValue(value),
            type: this.getEmailType(parameters),
            isPrimary: parameters.PREF === 'true' || parameters.PREF === '1'
        };
        contact.emails.push(email);
    }

    /**
     * تحليل العنوان
     * @param {string} value - العنوان المنظم
     * @param {Object} parameters - معاملات الخاصية
     * @param {Object} contact - كائن جهة الاتصال
     */
    parseAddress(value, parameters, contact) {
        const parts = value.split(';').map(part => this.decodeValue(part));
        const address = {
            poBox: parts[0] || '',
            extended: parts[1] || '',
            street: parts[2] || '',
            city: parts[3] || '',
            region: parts[4] || '',
            postalCode: parts[5] || '',
            country: parts[6] || '',
            type: this.getAddressType(parameters),
            isPrimary: parameters.PREF === 'true' || parameters.PREF === '1'
        };
        
        // إنشاء العنوان المنسق
        const addressParts = [
            address.street,
            address.city,
            address.region,
            address.postalCode,
            address.country
        ].filter(part => part.length > 0);
        address.formatted = addressParts.join(', ');
        
        contact.addresses.push(address);
    }

    /**
     * تحليل معاملات الخاصية
     * @param {Array} params - مصفوفة المعاملات
     * @returns {Object} كائن المعاملات
     */
    parseParameters(params) {
        const parameters = {};
        params.forEach(param => {
            const [key, value] = param.split('=');
            if (key && value) {
                parameters[key.toUpperCase()] = value.replace(/"/g, '');
            } else if (key) {
                parameters[key.toUpperCase()] = 'true';
            }
        });
        return parameters;
    }

    /**
     * فك تشفير القيمة
     * @param {string} value - القيمة المشفرة
     * @returns {string} القيمة المفكوكة
     */
    decodeValue(value) {
        if (!value) return '';

        // فك تشفير الأحرف المحجوزة
        return value
            .replace(/\\n/g, '\n')
            .replace(/\\,/g, ',')
            .replace(/\\;/g, ';')
            .replace(/\\\\/g, '\\');
    }

    /**
     * تنسيق رقم الهاتف
     * @param {string} phoneNumber - رقم الهاتف الخام
     * @returns {string} رقم الهاتف المنسق
     */
    formatPhoneNumber(phoneNumber) {
        if (!phoneNumber) return '';

        // إزالة جميع الأحرف غير الرقمية والرموز المسموحة
        let cleaned = phoneNumber.replace(/[^\d+\-\s()]/g, '');

        // إذا كان الرقم يبدأ بـ +966 (السعودية)
        if (cleaned.startsWith('+966')) {
            const number = cleaned.substring(4); // إزالة +966
            if (number.length === 9) {
                // تنسيق الرقم السعودي: +966 5XX XXX XXXX
                return `+966 ${number.substring(0, 3)} ${number.substring(3, 6)} ${number.substring(6)}`;
            }
        }

        // إذا كان الرقم يبدأ بـ 966 (بدون +)
        if (cleaned.startsWith('966') && cleaned.length === 12) {
            const number = cleaned.substring(3);
            return `+966 ${number.substring(0, 3)} ${number.substring(3, 6)} ${number.substring(6)}`;
        }

        // إذا كان رقم محلي سعودي (يبدأ بـ 05)
        if (cleaned.startsWith('05') && cleaned.length === 10) {
            return `+966 ${cleaned.substring(1, 4)} ${cleaned.substring(4, 7)} ${cleaned.substring(7)}`;
        }

        // إذا كان رقم محلي سعودي (يبدأ بـ 5)
        if (cleaned.startsWith('5') && cleaned.length === 9) {
            return `+966 ${cleaned.substring(0, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6)}`;
        }

        // إذا كان رقم أرضي سعودي (يبدأ بـ 01)
        if (cleaned.startsWith('01') && cleaned.length === 9) {
            return `+966 ${cleaned.substring(1, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6)}`;
        }

        // إذا كان رقم أرضي سعودي (يبدأ بـ 1)
        if (cleaned.startsWith('1') && cleaned.length === 8) {
            return `+966 1${cleaned.substring(1, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6)}`;
        }

        // تنسيق عام للأرقام الدولية
        if (cleaned.startsWith('+')) {
            return this.formatInternationalNumber(cleaned);
        }

        // إرجاع الرقم كما هو إذا لم يتطابق مع أي نمط
        return cleaned || phoneNumber;
    }

    /**
     * تنسيق الأرقام الدولية
     * @param {string} number - الرقم الدولي
     * @returns {string} الرقم المنسق
     */
    formatInternationalNumber(number) {
        // إزالة المسافات والرموز الإضافية
        const cleaned = number.replace(/[^\d+]/g, '');

        // تنسيق أساسي للأرقام الدولية
        if (cleaned.length > 10) {
            const countryCode = cleaned.substring(0, cleaned.length - 10);
            const mainNumber = cleaned.substring(cleaned.length - 10);

            if (mainNumber.length === 10) {
                return `${countryCode} ${mainNumber.substring(0, 3)} ${mainNumber.substring(3, 6)} ${mainNumber.substring(6)}`;
            }
        }

        return number;
    }

    /**
     * تحديد نوع الهاتف
     * @param {Object} parameters - معاملات الخاصية
     * @returns {string} نوع الهاتف
     */
    getPhoneType(parameters) {
        if (parameters.TYPE) {
            const types = parameters.TYPE.toLowerCase().split(',');
            if (types.includes('cell') || types.includes('mobile')) return 'mobile';
            if (types.includes('home')) return 'home';
            if (types.includes('work')) return 'work';
            if (types.includes('fax')) return 'fax';
        }
        return 'other';
    }

    /**
     * تحديد نوع البريد الإلكتروني
     * @param {Object} parameters - معاملات الخاصية
     * @returns {string} نوع البريد الإلكتروني
     */
    getEmailType(parameters) {
        if (parameters.TYPE) {
            const types = parameters.TYPE.toLowerCase().split(',');
            if (types.includes('home')) return 'home';
            if (types.includes('work')) return 'work';
        }
        return 'other';
    }

    /**
     * تحديد نوع العنوان
     * @param {Object} parameters - معاملات الخاصية
     * @returns {string} نوع العنوان
     */
    getAddressType(parameters) {
        if (parameters.TYPE) {
            const types = parameters.TYPE.toLowerCase().split(',');
            if (types.includes('home')) return 'home';
            if (types.includes('work')) return 'work';
        }
        return 'other';
    }

    /**
     * التحقق من صحة جهة الاتصال
     * @param {Object} contact - كائن جهة الاتصال
     * @returns {boolean} true إذا كانت جهة الاتصال صحيحة
     */
    isValidContact(contact) {
        return contact.name.full.length > 0 || 
               contact.phones.length > 0 || 
               contact.emails.length > 0;
    }

    /**
     * توليد معرف فريد
     * @returns {string} معرف فريد
     */
    generateId() {
        return 'contact_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * الحصول على جميع جهات الاتصال
     * @returns {Array} مصفوفة جهات الاتصال
     */
    getContacts() {
        return this.contacts;
    }

    /**
     * البحث في جهات الاتصال
     * @param {string} query - نص البحث
     * @returns {Array} جهات الاتصال المطابقة
     */
    searchContacts(query) {
        if (!query || query.trim().length === 0) {
            return this.contacts;
        }

        const searchTerm = query.toLowerCase().trim();
        return this.contacts.filter(contact => {
            // البحث في الاسم
            if (contact.name.full.toLowerCase().includes(searchTerm)) return true;
            
            // البحث في المؤسسة
            if (contact.organization.toLowerCase().includes(searchTerm)) return true;
            
            // البحث في أرقام الهاتف
            if (contact.phones.some(phone => phone.number.includes(searchTerm))) return true;
            
            // البحث في البريد الإلكتروني
            if (contact.emails.some(email => email.address.toLowerCase().includes(searchTerm))) return true;
            
            return false;
        });
    }

    /**
     * تصفية جهات الاتصال حسب النوع
     * @param {string} filterType - نوع التصفية
     * @returns {Array} جهات الاتصال المصفاة
     */
    filterContacts(filterType) {
        switch (filterType) {
            case 'phone':
                return this.contacts.filter(contact => contact.phones.length > 0);
            case 'email':
                return this.contacts.filter(contact => contact.emails.length > 0);
            case 'address':
                return this.contacts.filter(contact => contact.addresses.length > 0);
            default:
                return this.contacts;
        }
    }
}
