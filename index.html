<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض جهات الاتصال - Contact Viewer</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/brands.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="background">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <!-- Header -->
        <header class="glass-card header">
            <div class="header-content">
                <h1><i class="fas fa-address-book"></i> عارض جهات الاتصال</h1>
                <p>رفع وعرض ملفات VCard بتصميم حديث وأنيق</p>
            </div>
        </header>

        <!-- Upload Section -->
        <section class="glass-card upload-section" id="uploadSection">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h3>اسحب وأفلت ملف VCF هنا</h3>
                <p>أو انقر لاختيار الملف</p>
                <input type="file" id="fileInput" accept=".vcf,.vcard" multiple>
                <div class="upload-info">
                    <small>يدعم ملفات .vcf و .vcard</small>
                </div>
            </div>
        </section>

        <!-- Controls Section -->
        <section class="controls-section" id="controlsSection" style="display: none;">
            <div class="glass-card controls">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="البحث في جهات الاتصال...">
                </div>
                <div class="filter-container">
                    <select id="filterSelect">
                        <option value="all">جميع جهات الاتصال</option>
                        <option value="phone">لديهم أرقام هاتف</option>
                        <option value="email">لديهم بريد إلكتروني</option>
                        <option value="address">لديهم عنوان</option>
                    </select>
                </div>
                <div class="actions-container">
                    <button id="exportBtn" class="btn-action">
                        <i class="fas fa-download"></i> تصدير CSV
                    </button>
                    <button id="exportVcfBtn" class="btn-action">
                        <i class="fas fa-file-export"></i> تصدير VCF
                    </button>
                    <button id="printBtn" class="btn-action">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button id="favoritesBtn" class="btn-action">
                        <i class="fas fa-star"></i> المفضلة
                    </button>
                    <button id="clearBtn" class="btn-action btn-danger">
                        <i class="fas fa-trash"></i> مسح الكل
                    </button>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats-section" id="statsSection" style="display: none;">
            <div class="glass-card stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalContacts">0</div>
                    <div class="stat-label">إجمالي جهات الاتصال</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="withPhone">0</div>
                    <div class="stat-label">لديهم أرقام هاتف</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="withEmail">0</div>
                    <div class="stat-label">لديهم بريد إلكتروني</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="withAddress">0</div>
                    <div class="stat-label">لديهم عنوان</div>
                </div>
            </div>
        </section>

        <!-- Contacts Grid -->
        <section class="contacts-section" id="contactsSection" style="display: none;">
            <div class="contacts-grid" id="contactsGrid">
                <!-- Contacts will be dynamically inserted here -->
            </div>
        </section>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner" style="display: none;">
            <div class="spinner"></div>
            <p>جاري تحليل الملف...</p>
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-search"></i>
            <h3>لا توجد نتائج</h3>
            <p>جرب تغيير مصطلح البحث أو الفلتر</p>
        </div>
    </div>

    <!-- Contact Modal -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <div class="modal glass-card">
            <div class="modal-header">
                <h3 id="modalTitle">تفاصيل جهة الاتصال</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Contact details will be inserted here -->
            </div>
            <div class="modal-footer">
                <button class="btn-action" id="copyContactBtn">
                    <i class="fas fa-copy"></i> نسخ البيانات
                </button>
                <button class="btn-action" id="shareContactBtn">
                    <i class="fas fa-share"></i> مشاركة
                </button>
                <button class="btn-action" id="exportSingleBtn">
                    <i class="fas fa-download"></i> تصدير VCF
                </button>
                <button class="btn-action" id="qrCodeBtn">
                    <i class="fas fa-qrcode"></i> رمز QR
                </button>
                <button class="btn-action" id="callContactBtn">
                    <i class="fas fa-phone"></i> اتصال
                </button>
                <button class="btn-action" id="emailContactBtn">
                    <i class="fas fa-envelope"></i> بريد
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="vcf-parser.js"></script>
    <script src="script.js"></script>
</body>
</html>
